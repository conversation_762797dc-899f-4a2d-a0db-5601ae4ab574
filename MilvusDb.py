from pymilvus import Collection, connections, utility, MilvusClient
from pymilvus import FieldSchema, CollectionSchema, DataType
import numpy as np
import time
import requests


class Milvus:
    def __init__(self):
        self.alias = "default"
        self.host = '************'
        self.port = '19530'
        self.username = "root"
        self.password = "Milvus"

    
    def connect(self):
        """建立向量数据库连接"""
        try:
            connections.connect(
                alias=self.alias,
                host=self.host,
                port=self.port,
                user=self.username,
                password=self.password
            )
            print("成功连接到Milvus数据库")
            return True
        except Exception as e:
            print(f"Milvus连接错误: {e}")
            return False
        
    # 获取所有的集合名称
    def get_all_collections(self):
        """获取所有的集合名称"""
        collection_list = [] # 记录所有的集合信息
        # 获取所有集合名称
        collection_names = utility.list_collections()
        
        if not collection_names:
            print("Milvus 数据库中没有任何集合")
            return []

        print(f"共发现 {len(collection_names)} 个集合：\n")
        for collection_name in collection_names:
            print(collection_name)
            collection = self.get_collection(collection_name)
            # 格式化输出集合信息
            # 输出description描述
            collection_info = collection.description
            # 输出schema描述fields
            schema = collection.schema
            fields = schema.fields
            print("fields:",fields)
            print("description:",collection_info)
            collection_list.append(
                {
                    "name": collection_name,
                    "description": collection_info,
                    "fields": fields
                }
            )

        return collection_list

        
    def embedding_data(self, texts):
        """使用HTTP API进行词嵌入"""
        url = "http://***************:12000/embeddings"
        response = requests.post(url, json={"texts": texts})
        return response.json()["embeddings"]
    

    def get_collection(self, collection_name):
        """获取集合实例"""
        if not utility.has_collection(collection_name):
            print(f"集合 '{collection_name}' 不存在")
            return None
            
        collection = Collection(collection_name)
        collection.load()
        return collection

    
    # 批量查询多个collection的搜索
    def batch_search(self, query_texts, collection_names = None, anns_field="embedding", limit=100, nprobe=10, output_fields=None):

        """批量查询多个collection的搜索
        :param collection_names: 集合名称列表
        :param query_texts: 查询文本列表
        :param anns_field: 向量字段名
        :param limit: 返回结果数量
        :param nprobe: 搜索参数
        :param output_fields: 输出字段
        :return: 搜索结果列表
        """
        # 如果集合名称为空，则查询所有集合中的name形成新的列表
        if collection_names is None:
            collection_names = [collection["name"] for collection in self.get_all_collections()]

        # 存储所有结果
        all_results = []
        # 转换查询文本为向量
        query_vector = self.embedding_data([query_texts])[0]
        for coll_name in collection_names:
            collection = self.get_collection(coll_name) # 获取集合实例
            if not collection:
                continue
                   
            # 定义搜索参数
            search_params = {
                "metric_type": "L2",
                "params": {"nprobe": nprobe}
            }
            
            # output_fields默认返回所有字段
            if output_fields is None:
                output_field = collection.schema.fields # 获取fields下的name
                output_field = [field.name for field in output_field]

                
            # 执行批量搜索
            # 执行搜索
            results = collection.search(
                data=[query_vector],
                anns_field=anns_field,
                param=search_params,
                limit=limit,
                output_fields=output_field
            )
            # 解析搜索结果
            parse_results = self.parse_search_results(results)
            all_results.append(parse_results)
        
        return all_results[:limit]
    
    # 解析搜索后的向量检索结果
    def parse_search_results(self, results, field_name='combined_text') -> list:
        """解析搜索后的向量检索结果
        :param results: 搜索结果
        :return: 解析后的搜索结果
        """
        # 解析搜索结果
        parse_search_list = []
        
        for hit in results[0]:
            content = hit.entity.get(field_name, '')
            score = 1/(1+hit.score)
            parse_search_list.append({
                'content': content,
                'similarity_score': score,
            })
        
        return parse_search_list
    
    # 对batch_search函数中all_results检索结果进行去重和得分相信排序
    def batch_search_results_process(self, data):
        # 通用列表展平函数（支持任意层级嵌套）
        def flatten_list(nested_list):
            result = []
            for item in nested_list:
                if isinstance(item, list):
                    # 递归展平子列表
                    result.extend(flatten_list(item))
                else:
                    result.append(item)
            return result
        # 展平嵌套列表
        extracted_data = flatten_list(data)


        # 去重处理（根据content字段）
        seen_contents = set()
        distinct_data = []
        for item in extracted_data:
            content = item['content']
            if content not in seen_contents:
                seen_contents.add(content)
                distinct_data.append(item)

        # 按similarity_score降序排序
        sorted_data = sorted(distinct_data, key=lambda x: x['similarity_score'], reverse=True)

        # 输出结果
        for item in sorted_data:
            print(f"相似度: {item['similarity_score']}, 内容: {item['content']}")


if __name__ == '__main__':
    milvus = Milvus()
    # milvus.connect()
    # 批量查询多个collection的搜索
    query_texts = "豆粕价格"
    results = milvus.batch_search(query_texts)
    print(results)
    # 对batch_search函数中all_results检索结果进行去重和得分相信排序
    milvus.batch_search_results_process(results)
    

