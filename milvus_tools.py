"""
Milvus向量数据库相关工具
包含向量数据库连接、集合查询、向量搜索等功能
"""
from pydantic import BaseModel, Field
from typing import Any, Optional, List
from MilvusDb import Milvus
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field
from typing import Annotated, Optional
import os

# 参数赋值
MCP_name = "milvus_embedding(测试)"
MCP_host = "0.0.0.0"
MCP_port = "9085"
MCP_transport = 'sse'
# base_url = "http://public.inner.efunong.com/newerp/mcp"


# MCP Server 初始化
mcp = FastMCP(name = MCP_name, host = MCP_host, port = MCP_port)


# 定义通用的响应数据结构
class ResponseData(BaseModel):
    code: int = Field(default=200, title="编号", description="返回值编号，200代表正常，其他编号代表不同的含义，具体说明在message中")
    message: str = Field(title="消息", description="返回的说明信息")


# 定义集合字段信息数据结构
class CollectionFieldData(BaseModel):
    name: str = Field(title="字段名称", description="字段名称")
    type: str = Field(title="字段类型", description="字段数据类型")
    description: Optional[str] = Field(default=None, title="字段描述", description="字段描述信息")


# 定义集合信息数据结构
class CollectionData(BaseModel):
    name: str = Field(title="集合名称", description="集合名称")
    description: Optional[str] = Field(default=None, title="集合描述", description="集合描述信息")
    fields: List[Any] = Field(title="字段列表", description="集合中的字段信息列表")


# 定义获取所有集合的响应数据结构
class GetAllCollectionsResponse(ResponseData):
    data: Optional[List[CollectionData]] = Field(title="数据", description="返回的集合列表数据", nullable=True)


# 定义搜索结果数据结构
class SearchResultData(BaseModel):
    content: str = Field(title="内容", description="搜索结果内容")
    similarity_score: float = Field(title="相似度分数", description="相似度分数，范围0-1，越高越相似")


# 定义批量搜索的响应数据结构
class BatchSearchResponse(ResponseData):
    data: Optional[List[List[SearchResultData]]] = Field(title="数据", description="返回的搜索结果列表", nullable=True)


# 定义处理后搜索结果的响应数据结构
class ProcessedSearchResponse(ResponseData):
    data: Optional[List[SearchResultData]] = Field(title="数据", description="处理后的搜索结果列表", nullable=True)


# 定义连接响应数据结构
class ConnectResponse(ResponseData):
    data: Optional[bool] = Field(title="数据", description="连接是否成功", nullable=True)


# 定义词嵌入响应数据结构
class EmbeddingResponse(ResponseData):
    data: Optional[List[List[float]]] = Field(title="数据", description="词嵌入向量列表", nullable=True)


def register_tools(mcp):
    """注册Milvus相关工具到MCP实例"""
    
    # 创建Milvus实例
    milvus_instance = Milvus()
    # 连接Milvus数据库
    # milvus_instance.connect()
    
    @mcp.tool()
    def connect_milvus() -> ConnectResponse:
        """
        建立向量数据库连接
        """
        try:
            result = milvus_instance.connect()
            return ConnectResponse(
                code=200 if result else 500,
                message="成功连接到Milvus数据库" if result else "连接Milvus数据库失败",
                data=result
            )
        except Exception as e:
            return ConnectResponse(
                code=500,
                message=f"连接出错: {str(e)}",
                data=False
            )
    
    @mcp.tool()
    def get_all_collections() -> GetAllCollectionsResponse:
        """
        获取所有的集合名称和信息
        """
        try:
            collections = milvus_instance.get_all_collections()
            collection_data = []
            for collection in collections:
                collection_data.append(CollectionData(
                    name=collection["name"],
                    description=collection["description"],
                    fields=collection["fields"]
                ))
            
            return GetAllCollectionsResponse(
                code=200,
                message="成功获取所有集合信息",
                data=collection_data
            )
        except Exception as e:
            return GetAllCollectionsResponse(
                code=500,
                message=f"获取集合信息出错: {str(e)}",
                data=None
            )
    
    @mcp.tool()
    def embedding_data(
        texts: List[str] = Field(description="需要进行词嵌入的文本列表")
    ) -> EmbeddingResponse:
        """
        使用HTTP API进行词嵌入，将文本转换为向量
        """
        try:
            embeddings = milvus_instance.embedding_data(texts)
            return EmbeddingResponse(
                code=200,
                message="成功生成词嵌入向量",
                data=embeddings
            )
        except Exception as e:
            return EmbeddingResponse(
                code=500,
                message=f"词嵌入出错: {str(e)}",
                data=None
            )
    
    @mcp.tool()
    def batch_search(
        query_texts: str = Field(description="查询文本"),
        collection_names: Optional[List[str]] = Field(default=None, description="集合名称列表，如果不传则查询所有集合"),
        anns_field: str = Field(default="embedding", description="向量字段名"),
        limit: int = Field(default=100, description="返回结果数量"),
        nprobe: int = Field(default=10, description="搜索参数"),
        output_fields: Optional[List[str]] = Field(default=None, description="输出字段列表，如果为空则返回所有字段")
    ) -> BatchSearchResponse:
        """
        批量查询多个collection的搜索
        """
        try:
            results = milvus_instance.batch_search(
                query_texts=query_texts,
                collection_names=collection_names,
                anns_field=anns_field,
                limit=limit,
                nprobe=nprobe,
                output_fields=output_fields
            )
            
            # 转换结果格式
            formatted_results = []
            for collection_results in results:
                collection_data = []
                for result in collection_results:
                    collection_data.append(SearchResultData(
                        content=result["content"],
                        similarity_score=result["similarity_score"]
                    ))
                formatted_results.append(collection_data)
            
            return BatchSearchResponse(
                code=200,
                message="批量搜索完成",
                data=formatted_results
            )
        except Exception as e:
            return BatchSearchResponse(
                code=500,
                message=f"批量搜索出错: {str(e)}",
                data=None
            )

    @mcp.tool()
    def batch_search_results_process(
        search_results: List[List[dict]] = Field(description="批量搜索的原始结果数据")
    ) -> ProcessedSearchResponse:
        """
        对batch_search函数中all_results检索结果进行去重和得分相似度排序
        """
        try:
            # 调用Milvus实例的处理方法
            milvus_instance.batch_search_results_process(search_results)

            # 通用列表展平函数（支持任意层级嵌套）
            def flatten_list(nested_list):
                result = []
                for item in nested_list:
                    if isinstance(item, list):
                        # 递归展平子列表
                        result.extend(flatten_list(item))
                    else:
                        result.append(item)
                return result

            # 展平嵌套列表
            extracted_data = flatten_list(search_results)

            # 去重处理（根据content字段）
            seen_contents = set()
            distinct_data = []
            for item in extracted_data:
                content = item.get('content', '')
                if content not in seen_contents:
                    seen_contents.add(content)
                    distinct_data.append(item)

            # 按similarity_score降序排序
            sorted_data = sorted(distinct_data, key=lambda x: x.get('similarity_score', 0), reverse=True)

            # 转换为响应格式
            processed_results = []
            for item in sorted_data:
                processed_results.append(SearchResultData(
                    content=item.get('content', ''),
                    similarity_score=item.get('similarity_score', 0.0)
                ))

            return ProcessedSearchResponse(
                code=200,
                message="搜索结果处理完成",
                data=processed_results
            )
        except Exception as e:
            return ProcessedSearchResponse(
                code=500,
                message=f"搜索结果处理出错: {str(e)}",
                data=None
            )

    @mcp.tool()
    def get_collection_info(
        collection_name: str = Field(description="集合名称")
    ) -> GetAllCollectionsResponse:
        """
        获取指定集合的详细信息
        """
        try:
            collection = milvus_instance.get_collection(collection_name)
            if not collection:
                return GetAllCollectionsResponse(
                    code=404,
                    message=f"集合 '{collection_name}' 不存在",
                    data=None
                )

            # 获取集合信息
            collection_info = collection.description
            schema = collection.schema
            fields = schema.fields

            collection_data = [CollectionData(
                name=collection_name,
                description=collection_info,
                fields=fields
            )]

            return GetAllCollectionsResponse(
                code=200,
                message=f"成功获取集合 '{collection_name}' 信息",
                data=collection_data
            )
        except Exception as e:
            return GetAllCollectionsResponse(
                code=500,
                message=f"获取集合信息出错: {str(e)}",
                data=None
            )
if __name__ == '__main__':
    # 启动 MCP Server
    register_tools(mcp)
    mcp.run(transport=MCP_transport)