"""
合同相关工具
包含合同查询、创建、修改等功能
"""
import requests
from pydantic import BaseModel, Field
from typing import Any, Optional


# 定义通用的响应数据结构
class ResponseData(BaseModel):
    code: int = Field(default=200, title="编号", description="返回值编号，200代表正常，其他编号代表不同的含义，具体说明在message中")
    message: str = Field(title="消息", description="返回的说明信息")


# 定义合同数据结构
class ContractData(BaseModel):
    orderId: Optional[int] = Field(default=None, description="订单记录的Id", title="订单Id")
    orderCode: Optional[str] = Field(default=None, title="订单编号", description="订单记录的Code")
    count: Optional[float] = Field(default=None, title="数量", description="订单数量")
    price: Optional[float] = Field(default=None, title="价格", description="价格")
    deliveryStartTime: Optional[str] = Field(default=None, title="提货开始时间", description="提货起始日期，时间取00:00:00")
    deliveryEndTime: Optional[str] = Field(default=None, title="提货结束时间", description="提货截止日期，时间取23:00:00")
    goodsName: Optional[str] = Field(default=None, title="商品名称", description="商品名称或者货品名称")
    id: Optional[int] = Field(default=None, title="合同Id", description="合同记录Id")
    contractCode: Optional[str] = Field(default=None, title="合同编号", description="合同编号")
    type: Optional[int] = Field(default=None, title="合同类型", description="合同类型，1：基差合同，2：暂定价合同，3：一口价/现货价合同，4：点价合同，5：结价合同，6：基差移库合同，7：一口价移库合同", enum=[1, 2, 3, 4, 5, 6, 7])
    contractPurchaseType: Optional[int] = Field(default=None, title="合同采购类型，1：采购，2：销售", enum=[1, 2], description="合同采购类型，又称合同销售类型")
    goodsId: Optional[int] = Field(default=None, title="商品Id", description="商品记录的Id")
    contractImage: Optional[str] = Field(default=None, title="电子合同图片", description="电子合同的图片文件")
    status: Optional[int] = Field(default=None, title="状态", description="合同状态，1：草拟，2：待审核，3：待付保证金，4：执行中，5：移库结算中，6：已终结") # , enum=[1, 2, 3, 4, 5, 6]
    customerId: Optional[int] = Field(default=None, title="客户Id", description="客户记录的Id")
    customerName: Optional[str] = Field(default=None, title="客户名称", description="客户名称")


# 定义获取合同信息的响应数据结构
class GetContractInfoResponse(ResponseData):
    data: Optional[ContractData] = Field(title="数据", description="返回的具体数据，类型为泛型对象", nullable=True)

# 定义单个账单数据结构
class BillData(BaseModel):
    id: Optional[int] = Field(default=None, title="账单Id", description="账单记录Id")
    status: Optional[int] = Field(default=None, title="状态", description="账单处理状态，0：待核销，1：已核销，2：已作废，3：已申请付款") #  enum=[0, 1, 2, 3]
    code: Optional[str] = Field(default=None, title="账单编号", description="账单编号")
    customerId: Optional[int] = Field(default=None, title="客户Id", description="客户记录Id")
    customerName: Optional[str] = Field(default=None, title="客户名称", description="客户名称")
    billType: Optional[int] = Field(default=None, title="账单类型", description="账单类型，0：其他，1：开户保证金，2：预付款，3：合同保证金，4：合同保证金追加，5：货款，6：销售合同短溢款，7：运费，8：补偿款，9：销售合同提货款，10：采购合同提货款，11：采购合同短溢款，12：采购商退款，13：转出保证金，14：转入保证金，15：转出预付款，16：转入预付款，17：销售合同结算退款，18：货款抵扣保证金，19：货款抵扣预付款，20：提货使用优惠券，21：余额转账转出，22：余额转账转入，23：向信用账户借款，24：向信用账户还款，25：销售合同回购记录账单，26：开票费，27：保证金释放到账户余额，28：上家账户余额抵扣定金，29：上家账户余额抵扣预付款，30：保证金抵扣预付款，31：预付款释放到账户余额，32：供应商退款", enum=[i for i in range(33)])
    money: Optional[float] = Field(default=None, title="账单总金额", description="账单总金额")
    practicalMoney: Optional[float] = Field(default=None, title="账单实付金额", description="应收、应付资金金额")
    contractId: Optional[int] = Field(default=None, title="合同Id", description="账单关联的合同记录Id")
    deliveryId: Optional[int] = Field(default=None, title="提货Id", description="账单关联的提货记录Id")


# 定义获取客户未核销/未支付账单的响应数据结构
class GetCustomerWaitPayMoneyBillsResponse(ResponseData):
    data: Optional[BillData] = Field(title="数据", description="返回的具体数据，类型为泛型对象", nullable=True)


# 定义提货单详情信息响应中的数据结构
class DeliveryOrderData(BaseModel):
    id: Optional[int] = Field(default=None, title="提货Id", description="提货Id，提货单Id，提货记录Id")
    deliveryCode: Optional[str] = Field(default=None, title="提货编号", description="提货编号")
    applyId: Optional[int] = Field(default=None, title="提货申请Id", description="提货申请Id")
    customerId: Optional[int] = Field(default=None, title="客户Id", description="客户Id")
    goodsName: Optional[str] = Field(default=None, title="商品名称", description="商品名称")
    deliveryTotalNumber: Optional[float] = Field(default=None, title="提货总数量", description="提货总数量")
    deliveryTotalMoney: Optional[float] = Field(default=None, title="提货总金额", description="提货总金额")
    agentInfo: Optional[str] = Field(default=None, title="提货委托信息", description="提货委托信息")
    agentImage: Optional[str] = Field(default=None, title="提货委托书图片", description="提货委托书图片URL")
    sellerContractInfo: list[Any] = Field(title="提货销售合同信息", description="提货单关联的销售合同信息")
    buyerContractInfo: list[Any] = Field(title="提货采购合同信息", description="提货单关联的采购合同信息")
    deliveryStatus: int = Field(title="提货状态", description="提货单状态，提货状态，-1：已作废，0：草稿，1：待核准，2：待开单勾兑，3：待出磅单，7：销售移库合同待结算，4：待财务确认，5：已生成短溢款，6：已完成，8：磅单审核", enum=[-1, 0, 1, 2, 3, 7, 4, 5, 6, 8])


# 定义获取提货单详情信息的响应数据结构
class DeliveryOrderResponse(ResponseData):
    data: Optional[DeliveryOrderData] = Field(title="数据", description="返回的具体数据，类型为泛型对象", nullable=True)


# 定义创建销售合同响应中的数据结构
class CreateSalesContractResponseData(BaseModel):
    orderId: Optional[int] = Field(default=None, title="订单Id", description="订单记录的Id")
    orderCode: Optional[str] = Field(default=None, title="订单编号", description="订单记录的Code")
    count: Optional[float] = Field(default=None, title="数量", description="订单数量")
    price: Optional[float] = Field(default=None, title="价格", description="价格")
    deliveryStartTime: Optional[str] = Field(title="提货开始时间", description="提货起始日期，时间取00:00:00")
    deliveryEndTime: Optional[str] = Field(title="提货结束时间", description="提货截止日期，时间取23:00:00")
    goodsName: Optional[str] = Field(title="商品名称", description="商品名称或者货品名称")
    id: Optional[int] = Field(default=None, title="合同Id", description="合同记录Id")
    contractCode: Optional[str] = Field(default=None, title="合同编号", description="合同编号")
    type: Optional[int] = Field(default=None, title="合同类型", description="合同类型，1：基差合同，2：暂定价合同，3：一口价/现货价合同，4：点价合同，5：结价合同，6：基差移库合同，7：一口价移库合同", enum=[1, 2, 3, 4, 5, 6, 7])
    contractPurchaseType: Optional[int] = Field(default=None, title="合同采购类型", enum=[1, 2],
                                      description="合同采购类型，又称合同销售类型，1：采购合同，2：销售合同")
    goodsId: Optional[int] = Field(default=None, title="商品Id", description="商品记录的Id")
    contractImage: Optional[str] = Field(default=None, title="电子合同图片", description="电子合同的图片文件")
    status: Optional[int] = Field(default=None, title="状态", description="合同状态，1：草拟，2：待审核，3：待付保证金，4：执行中，5：移库结算中，6：已终结") # enum=[1, 2, 3, 4, 5, 6]


# 定义创建销售合同的响应数据结构
class CreateSalesContractResponse(ResponseData):
    data: Optional[CreateSalesContractResponseData] = Field(title="数据",
                                                            description="返回的具体数据，类型为泛型对象",
                                                            nullable=True)


# 定义支付合同账单的响应数据结构
class PayContractMoneyBillsResponse(ResponseData):
    data: Optional[str] = Field(title="数据", description="返回的具体数据，类型为泛型对象", nullable=True)


# 定义修改合同信息响应数据中的data结构
class ModifySalesContractResponseData(BaseModel):
    orderId: Optional[int] = Field(default=None, title="订单Id", description="订单记录的Id")
    orderCode: Optional[str] = Field(default=None, title="订单编号", description="订单记录的Code")
    count: float = Field(title="数量", description="订单数量")
    price: float = Field(title="价格", description="价格")
    deliveryStartTime: str = Field(title="提货开始时间", description="提货起始日期，时间取00:00:00")
    deliveryEndTime: str = Field(title="提货结束时间", description="提货截止日期，时间取23:00:00")
    goodsName: str = Field(title="商品名称", description="商品名称或者货品名称")
    id: Optional[int] = Field(default=None, title="合同Id", description="合同记录Id")
    contractCode: Optional[str] = Field(default=None, title="合同编号", description="合同编号")
    type: Optional[int] = Field(default=None, title="合同类型", description="合同类型，1：基差合同，2：暂定价合同，3：一口价/现货价合同，4：点价合同，5：结价合同，6：基差移库合同，7：一口价移库合同", enum=[1, 2, 3, 4, 5, 6, 7])
    contractPurchaseType: Optional[int] = Field(default=None, title="合同采购类型", enum=[1, 2], description="合同采购类型，又称合同销售类型，1：采购合同，2：销售合同")
    goodsId: Optional[int] = Field(default=None, title="商品Id", description="商品记录的Id")
    contractImage: Optional[str] = Field(default=None, title="电子合同图片", description="电子合同的图片文件")
    status: Optional[int] = Field(default=None, title="状态", description="合同状态，，1：草拟，2：待审核，3：待付保证金，4：执行中，5：移库结算中，6：已终结", enum=[1, 2, 3, 4, 5, 6])


# 定义修改合同信息的响应数据结构
class ModifySalesContractResponse(ResponseData):
    data: Optional[ModifySalesContractResponseData] = Field(title="数据", description="返回的具体数据，类型为泛型对象", nullable=True)


# 定义合同新增保证金账单的响应数据结构
class AddContractDepositMoneyBillsResponse(ResponseData):
    data: Optional[dict] = Field(title="数据", description="返回的具体数据，类型为泛型对象", nullable=True)


def register_tools(mcp):
    """注册通用相关工具到MCP实例"""
    # 从配置中获取base_url
    config = mcp.config_manager.get_tools_config() if hasattr(mcp, 'config_manager') else {}
    base_url = config.get('contract', {}).get('base_url', 'http://public.inner.efunong.com/newerp/mcp')

    @mcp.tool()
    def getContractInfo(
            id: str = Field(default=None, description="合同Id，Id和Code至少传一个"),
            code: str = Field(default=None, description="合同编号，Id和Code至少传一个"),
            fn_token: str = Field(default=None, description="AccessToken，应先获取", example="")
    ) -> GetContractInfoResponse:
        """
        根据合同Id或者合同编号获取合同信息，Id和Code至少传一个
        """
        headers = {}
        params = {}
        if id is not None:
            params["id"] = id
        if code is not None:
            params["code"] = code
        if fn_token is not None:
            headers["fn-token"] = fn_token
        response = requests.get(f"{base_url}/getContractInfo", params=params, headers=headers)
        try:
            result = response.json()
            data = result.get("data")
            if data is not None:
                data = ContractData(**data).model_dump()
            return GetContractInfoResponse(
                code=result["code"],
                message=result["message"],
                data=data
            )
        except Exception as e:
            return GetContractInfoResponse(
                code=500,
                message=f"请求出错: {str(e)}",
                data=None
            )
    
    @mcp.tool()
    def getCustomerWaitPayMoneyBills(
            customerId: str = Field(default=None, description=""),
            fn_token: str = Field(default=None, description="AccessToken，应先获取", example="")
    ) -> GetCustomerWaitPayMoneyBillsResponse:
        """
        获取客户所有未核销/未支付的账单。
        """
        headers = {}
        params = {}
        if customerId is not None:
            params["customerId"] = customerId
        if fn_token is not None:
            headers["fn-token"] = fn_token
        response = requests.get(f"{base_url}/getCustomerWaitPayMoneyBills", params=params, headers=headers)
        try:
            result = response.json()
            bills = []
            if result["data"] is not None:
                for bill in result["data"]:
                    bills.append(BillData(**bill).model_dump())
            return GetCustomerWaitPayMoneyBillsResponse(
                code=result["code"],
                message=result["message"],
                data=bills
            )
        except Exception as e:
            return GetCustomerWaitPayMoneyBillsResponse(
                code=500,
                message=f"请求出错: {str(e)}",
                data=None
            )
    
    @mcp.tool()
    def getDeliveryOrderInfo(
            id: str = Field(default=None, description="提货单Id"),
            fn_token: str = Field(default=None, description="AccessToken，应先获取", example="")
    ) -> DeliveryOrderResponse:
        """
        获取提货单详情信息
        """
        headers = {}
        params = {}
        if id is not None:
            params["id"] = id
        if fn_token is not None:
            headers["fn-token"] = fn_token
        response = requests.get(f"{base_url}/getDeliveryOrderInfo", params=params, headers=headers)
        try:
            result = response.json()
            data = result.get("data")
            if data is not None:
                data = DeliveryOrderData(**data).model_dump()
            return DeliveryOrderResponse(
                code=result["code"],
                message=result["message"],
                data=data
            )
        except Exception as e:
            return DeliveryOrderResponse(
                code=500,
                message=f"请求出错: {str(e)}",
                data=None
            )
        
    @mcp.tool()
    def createSalesContract(
            fn_token: str = Field(default=None, description="AccessToken，应先获取", example=""),
            orderId: int = Field(default=None, title="订单Id", description="订单记录的Id"),
            orderCode: str = Field(default=None, title="订单编号", description="订单记录的Code"),
            count: float = Field(description="订单数量", title="数量"),
            price: float = Field(description="价格", title="价格"),
            deliveryStartTime: str = Field(title="提货开始时间", description="提货起始日期，时间取00:00:00"),
            deliveryEndTime: str = Field(title="提货结束时间", description="提货截止日期，时间取23:00:00"),
            goodsName: str = Field(title="商品名称", description="商品名称或者货品名称"),
            id: int = Field(default=None, title="合同Id", description="合同记录Id"),
            contractCode: str = Field(default=None, title="合同编号", description="合同编号"),
            type: int = Field(default=None, title="合同类型", description="合同类型，1：基差合同，2：暂定价合同，3：一口价/现货价合同，4：点价合同，5：结价合同，6：基差移库合同，7：一口价移库合同", enum=[1, 2, 3, 4, 5, 6, 7]),
            contractPurchaseType: int = Field(default=None, title="合同采购类型", enum=[1, 2],
                                              description="合同采购类型，又称合同销售类型，1：采购合同，2：销售合同"),
            goodsId: int = Field(default=None, title="商品Id", description="商品记录的Id"),
            contractImage: str = Field(default=None, title="电子合同图片", description="电子合同的图片文件"),
            status: int = Field(default=None, title="状态", description="合同状态，1：草拟，2：待审核，3：待付保证金，4：执行中，5：移库结算中，6：已终结", enum=[1, 2, 3, 4, 5, 6])
    ) -> CreateSalesContractResponse:
        """
        根据订单Id或者订单编号创建销售合同
        """
        headers = {}
        params = {
            "orderId": orderId, "orderCode": orderCode, "count": count, "price": price,
            "deliveryStartTime": deliveryStartTime, "deliveryEndTime": deliveryEndTime, "goodsName": goodsName,
            "id": id, "contractCode": contractCode, "type": type, "contractPurchaseType": contractPurchaseType,
            "goodsId": goodsId, "contractImage": contractImage, "status": status
        }
        if fn_token is not None:
            headers["fn-token"] = fn_token
        response = requests.post(f"{base_url}/createSalesContract", json=params, headers=headers)
        try:
            result = response.json()
            data = result.get("data")
            if data is not None:
                data_obj = CreateSalesContractResponseData(**data).model_dump()
            else:
                data_obj = None
            return CreateSalesContractResponse(
                code=result["code"],
                message=result["message"],
                data=data_obj
            )
        except Exception as e:
            return CreateSalesContractResponse(
                code=500,
                message=f"请求出错: {str(e)}",
                data=None
            )

    @mcp.tool()
    def payContractMoneyBills(
            fn_token: str = Field(default=None, description="AccessToken，应先获取", example=""),
            contractId: int = Field(default=None, title="合同Id", description="合同记录Id"),
            contractCode: str = Field(default=None, title="合同编号", description="合同编号"),
            operationType: int = Field(default=1, title="操作类型", description="操作类型，NEW：新建合同，NEW_FROM_MODIFY：变更新增合同，\
                                       NEW_FROM_CONFIRM：点价新增合同，PAY_DEPOSITL：付保证金，PAY_BOOKING：付预付款，MODIFY：变更，\
                                       FINISH：合同终结，CONFIRM_PRICE：点价，CONFIRM_PRICE_APPLY：点价申请，DELIVERY_APPLY：提货申请，\
                                       DELIVERY：提货，DELIVERY_SETTLE：提货结算，DEPOSIT_WITHDRAW：保证金提现到余额，\
                                       CUSTOMER_MONEY_WITHDRAW：余额提现，TRANSFER：转账，CREDIT：信用支付，RINSE：洗盘，\
            #                            TRANSFER_MONTH：转月，OTHER：其他", 
            # enum=[
            #     "NEW",
            #     "NEW_FROM_MODIFY",
            #     "NEW_FROM_CONFIRM",
            #     "PAY_DEPOSITL",
            #     "PAY_BOOKING",
            #     "MODIFY",
            #     "FINISH",
            #     "CONFIRM_PRICE",
            #     "CONFIRM_PRICE_APPLY",
            #     "DELIVERY_APPLY",
            #     "DELIVERY",
            #     "DELIVERY_SETTLE",
            #     "DEPOSIT_WITHDRAW",
            #     "CUSTOMER_MONEY_WITHDRAW",
            #     "TRANSFER",
            #     "CREDIT",
            #     "RINSE",
            #     "TRANSFER_MONTH",
            #     "OTHER"
            # ]
            ),
            operationSource: str = Field(default="agent", title="操作来源", enum=[
                "erp",
                "agent"
            ], description="通过哪个系统操作的，默认为Agent,1：ERP系统，2：智能体Agent"),
            memo: str = Field(default="", title="备注", description="备注")
    ) -> PayContractMoneyBillsResponse:
        """
        支付合同的账单，核销合同的待支付/待核销账单
        """
        headers = {}
        params = {
            "contractId": contractId,
            "contractCode": contractCode,
            "operationType": operationType,
            "operationSource": operationSource,
            "memo": memo
        }
        if fn_token is not None:
            headers["fn-token"] = fn_token
        response = requests.post(f"{base_url}/payContractMoneyBills", json=params, headers=headers)
        try:
            result = response.json()
            return PayContractMoneyBillsResponse(
                code=result["code"],
                message=result["message"],
                data=result["data"]
            )
        except Exception as e:
            return PayContractMoneyBillsResponse(
                code=500,
                message=f"请求出错: {str(e)}",
                data=None
            )
    @mcp.tool()
    def modifySalesContract(
            fn_token: str = Field(default=None, description="AccessToken，应先获取", example=""),
            orderId: int = Field(default=None, title="订单Id", description="订单记录的Id"),
            orderCode: str = Field(default=None, title="订单编号", description="订单记录的Code"),
            count: float = Field(title="数量", description="订单数量"),
            price: float = Field(title="价格", description="价格"),
            deliveryStartTime: str = Field(title="提货开始时间", description="提货起始日期，时间取00:00:00"),
            deliveryEndTime: str = Field(title="提货结束时间", description="提货截止日期，时间取23:00:00"),
            goodsName: str = Field(title="商品名称", description="商品名称或者货品名称"),
            id: int = Field(default=None, title="合同Id", description="合同记录Id"),
            contractCode: str = Field(default=None, title="合同编号", description="合同编号"),
            type: int = Field(default=None, title="合同类型", description="合同类型，1：基差合同，2：暂定价合同，3：一口价/现货价合同，4：点价合同，5：结价合同，6：基差移库合同，7：一口价移库合同", enum=[1, 2, 3, 4, 5, 6, 7]),
            contractPurchaseType: int = Field(default=None, title="合同采购类型", enum=[1, 2], description="合同采购类型，又称合同销售类型，1：采购合同，2：销售合同"),
            goodsId: int = Field(default=None, title="商品Id", description="商品记录的Id"),
            contractImage: str = Field(default=None, title="电子合同图片", description="电子合同的图片文件"),
            status: int = Field(default=None, title="状态", description="合同状态，，1：草拟，2：待审核，3：待付保证金，4：执行中，5：移库结算中，6：已终结", enum=[1, 2, 3, 4, 5, 6])
    ) -> ModifySalesContractResponse:
        """
        修改合同信息
        """
        headers = {}
        params = {
            "orderId": orderId, "orderCode": orderCode, "count": count, "price": price,
            "deliveryStartTime": deliveryStartTime, "deliveryEndTime": deliveryEndTime, "goodsName": goodsName,
            "id": id, "contractCode": contractCode, "type": type,
            "contractPurchaseType": contractPurchaseType, "goodsId": goodsId,
            "contractImage": contractImage, "status": status
        }
        if fn_token is not None:
            headers["fn-token"] = fn_token
        response = requests.post(f"{base_url}/modifySalesContract", json=params, headers=headers)
        try:
            result = response.json()
            data = result.get("data")
            if data is not None:
                data = ModifySalesContractResponseData(**data).model_dump()
            return ModifySalesContractResponse(
                code=result["code"],
                message=result["message"],
                data=data
            )
        except Exception as e:
            return ModifySalesContractResponse(
                code=500,
                message=f"请求出错: {str(e)}",
                data=None
            )
        
    @mcp.tool()
    def addContractDepositMoneyBills(
            fn_token: str = Field(default=None, description="AccessToken，应先获取", example=""),
            id: int = Field(default=None, title="账单Id", description="账单记录Id"),
            status: int = Field(default=None, title="状态", description="账单处理状态，0：待核销，1：已核销，2：已作废，3：已申请付款", enum=[0,1,2,3]),
            code: str = Field(default=None, title="账单编号", description="账单编号"),
            customerId: int = Field(default=None, title="客户Id", description="客户记录Id"),
            customerName: str = Field(default=None, title="客户名称", description="客户名称"),
            billType: int = Field(title="账单类型", description="账单类型，0：其他，1：开户保证金，2：预付款，3：合同保证金，4：合同保证金追加，5：货款，6：销售合同短溢款，\
                                  7：运费，8：补偿款，9：销售合同提货款，10：采购合同提货款，11：采购合同短溢款，12：采购商退款，13：转出保证金，\
                                  14：转入保证金，15：转出预付款，16：转入预付款，17：销售合同结算退款，18：货款抵扣保证金，19：货款抵扣预付款，\
                                  20：提货使用优惠券，21：余额转账转出，22：余额转账转入，23：向信用账户借款，24：向信用账户还款，25：销售合同回购记录账单，\
                                  26：开票费，27：保证金释放到账户余额，28：上家账户余额抵扣定金，29：上家账户余额抵扣预付款，30：保证金抵扣预付款，\
                                  31：预付款释放到账户余额，32：供应商退款", enum=[i for i in range(33)]),
            money: float = Field(title="账单总金额", description="账单总金额"),
            practicalMoney: float = Field(title="账单实付金额", description="应收、应付资金金额"),
            contractId: int = Field(default=None, title="合同Id", description="账单关联的合同记录Id"),
            deliveryId: int = Field(default=None, title="提货Id", description="账单关联的提货记录Id")
    ) -> AddContractDepositMoneyBillsResponse:
        """
        合同新增保证金账单
        """
        headers = {}
        params = {
            "id": id, "status": status, "code": code, "customerId": customerId, "customerName": customerName,
            "billType": billType, "money": money, "practicalMoney": practicalMoney, "contractId": contractId,
            "deliveryId": deliveryId
        }
        if fn_token is not None:
            headers["fn-token"] = fn_token
        response = requests.post(f"{base_url}/addContractDepositMoneyBills", json=params, headers=headers)
        try:
            result = response.json()
            return AddContractDepositMoneyBillsResponse(
                code=result["code"],
                message=result["message"],
                data=result["data"]
            )
        except Exception as e:
            return AddContractDepositMoneyBillsResponse(
                code=500,
                message=f"请求出错: {str(e)}",
                data=None
            )